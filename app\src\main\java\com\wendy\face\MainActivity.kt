package com.wendy.face

import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.WindowCompat
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.analyzer.FaceAnalyzer
import com.wendy.face.analyzer.PalaceAnalysisResult
import com.wendy.face.ui.components.CameraView
import com.wendy.face.ui.components.FaceOverlay
import com.wendy.face.ui.screens.FaceAnalysisScreen
import com.wendy.face.ui.theme.FaceTheme
import com.wendy.face.utils.ImageUtils
import com.wendy.face.utils.PermissionUtils

/**
 * 主Activity - 重构后的简化版本
 * 负责权限管理和UI状态协调
 */
class MainActivity : ComponentActivity() {

    // 权限请求启动器
    private val requestMultiplePermissionsLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            permissions.entries.forEach { entry ->
                val permission = entry.key
                val isGranted = entry.value
                if (isGranted) {
                    Log.d("MainActivity", "Permission granted: $permission")
                } else {
                    Log.w("MainActivity", "Permission denied: $permission")
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置全面屏模式
        setupFullScreenMode()

        // 检查并请求权限
        val permissionsToRequest = PermissionUtils.getPermissionsToRequest(this)
        if (permissionsToRequest.isNotEmpty()) {
            requestMultiplePermissionsLauncher.launch(permissionsToRequest)
        }

        setContent {
            FaceTheme {
                FaceDetectionApp()
            }
        }
    }

    /**
     * 设置全面屏模式，隐藏状态栏和导航栏
     */
    private fun setupFullScreenMode() {
        try {
            // 启用边到边显示
            WindowCompat.setDecorFitsSystemWindows(window, false)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ 使用新的WindowInsetsController
                window.insetsController?.let { controller ->
                    controller.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                    controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                }
            } else {
                // Android 10及以下版本
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                )
            }

            // 设置窗口标志
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to setup full screen mode", e)
            // 如果全面屏设置失败，至少隐藏状态栏
            try {
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN
            } catch (ex: Exception) {
                Log.e("MainActivity", "Failed to hide status bar", ex)
            }
        }
    }
}

/**
 * 人脸检测应用主界面
 */
@Composable
fun FaceDetectionApp() {
    // 状态管理
    var faceMeshes by remember { mutableStateOf<List<FaceMesh>>(emptyList()) }
    var imageWidth by remember { mutableStateOf(0) }
    var imageHeight by remember { mutableStateOf(0) }
    var isBackCamera by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<Uri?>(null) }
    var capturedBitmap by remember { mutableStateOf<Bitmap?>(null) }
    var capturedFaceMeshes by remember { mutableStateOf<List<FaceMesh>>(emptyList()) }
    var capturedImageWidth by remember { mutableStateOf(0) }
    var capturedImageHeight by remember { mutableStateOf(0) }
    var showCamera by remember { mutableStateOf(true) }
    val faceDetectorManager by remember { mutableStateOf(com.wendy.face.detection.FaceDetectorManager()) }
    var showCroppedImage by remember { mutableStateOf(false) }
    var croppedBitmap by remember { mutableStateOf<Bitmap?>(null) }

    // 新增状态
    val faceAnalyzer by remember { mutableStateOf(FaceAnalyzer()) }
    var analysisResults by remember { mutableStateOf<List<PalaceAnalysisResult>>(emptyList()) }
    var showAnalysisScreen by remember { mutableStateOf(false) }

    Box(modifier = Modifier.fillMaxSize()) {
        when {
            showAnalysisScreen && capturedBitmap != null && analysisResults.isNotEmpty() -> {
                // 显示分析结果界面
                FaceAnalysisScreen(
                    capturedBitmap = capturedBitmap!!,
                    faceMeshes = capturedFaceMeshes,
                    analysisResults = analysisResults,
                    isBackCamera = isBackCamera,
                    onBack = {
                        // 重置状态以返回相机
                        showCamera = true
                        showAnalysisScreen = false
                        capturedImageUri = null
                        capturedBitmap = null
                        capturedFaceMeshes = emptyList()
                        analysisResults = emptyList()
                    }
                )
            }
            showCamera -> {
            // 显示相机预览界面
            CameraView(
                isBackCamera = isBackCamera,
                onFacesDetected = { detectedFaceMeshes, width, height ->
                    faceMeshes = detectedFaceMeshes
                    imageWidth = width
                    imageHeight = height
                },
                onCameraSwitch = { isBackCamera = !isBackCamera },
                onImageCaptured = { uri, bitmap ->
                    capturedImageUri = uri
                    capturedBitmap = bitmap
                    showCamera = false // 隐藏相机

                    // 先清空之前的检测结果
                    capturedFaceMeshes = emptyList()
                    capturedImageWidth = 0
                    capturedImageHeight = 0
                    analysisResults = emptyList()
                    showAnalysisScreen = false

                    // 对捕获的高清图进行人脸识别和分析
                    bitmap?.let { bmp ->
                        Log.d("FaceDetectionApp", "Processing captured bitmap: ${bmp.width}x${bmp.height}, isBackCamera: $isBackCamera")
                        val inputImage = InputImage.fromBitmap(bmp, 0)

                        // 添加一个小延迟确保UI状态已更新
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            faceDetectorManager.detectFaces(
                                inputImage = inputImage,
                                onSuccess = { detectedFaceMeshes, width, height ->
                                    capturedFaceMeshes = detectedFaceMeshes
                                    capturedImageWidth = width
                                    capturedImageHeight = height
                                    Log.d("FaceDetectionApp", "Face meshes detected on captured image: ${detectedFaceMeshes.size}")
                                    Log.d("FaceDetectionApp", "Captured image dimensions: ${width}x${height}")

                                    // 如果检测到人脸，则进行分析
                                    if (detectedFaceMeshes.isNotEmpty()) {
                                        Log.d("FaceDetectionApp", "Analyzing face and showing analysis screen")
                                        analysisResults = faceAnalyzer.analyze(detectedFaceMeshes.first())
                                        showAnalysisScreen = true // 显示分析界面
                                    } else {
                                        // 未检测到人脸，停留在预览图界面，让用户自己决定
                                        Log.d("FaceDetectionApp", "No face detected on captured image.")
                                        // 清空可能存在的旧分析结果
                                        analysisResults = emptyList()
                                    }
                                },
                                onFailure = { e ->
                                    Log.e("FaceDetectionApp", "Face mesh detection on captured image failed", e)
                                    // 检测失败，同样停留在预览图界面
                                    analysisResults = emptyList()
                                }
                            )
                        }, 100) // 100ms延迟
                    }
                }
            )
            }
            else -> {
                // Fallback: 显示拍摄的照片，处理分析前或失败的场景
                capturedBitmap?.let { bitmap ->
                    Box(modifier = Modifier.fillMaxSize()) {
                        Image(
                            bitmap = bitmap.asImageBitmap(),
                            contentDescription = "Captured Photo",
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop // 改为Crop以匹配相机预览
                        )
                        // 在照片上叠加人脸框，确保它在Image组件之上
                        FaceOverlay(
                            faceMeshes = capturedFaceMeshes,
                            imageWidth = capturedImageWidth,
                            imageHeight = capturedImageHeight,
                            isBackCamera = isBackCamera
                        )

                        // 添加调试信息显示
                        if (capturedFaceMeshes.isNotEmpty()) {
                            Text(
                                text = "检测到 ${capturedFaceMeshes.size} 张人脸\n图片尺寸: ${capturedImageWidth}x${capturedImageHeight}\n位图尺寸: ${bitmap.width}x${bitmap.height}",
                                color = androidx.compose.ui.graphics.Color.Yellow,
                                modifier = Modifier
                                    .align(Alignment.TopStart)
                                    .padding(16.dp)
                                    .background(androidx.compose.ui.graphics.Color.Black.copy(alpha = 0.7f))
                                    .padding(8.dp)
                            )
                        }
                    }
                }
                // 提供返回按钮
                Column(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = {
                            // 重置状态以返回相机
                            showCamera = true
                            capturedImageUri = null
                            capturedBitmap = null
                            capturedFaceMeshes = emptyList()
                            analysisResults = emptyList()
                        }
                    ) {
                        Text("返回摄像头")
                    }

                    // 添加测试按钮
                    Button(
                        onClick = {
                            // 强制重新检测人脸
                            capturedBitmap?.let { bmp ->
                                Log.d("FaceDetectionApp", "Manual face detection triggered")
                                val inputImage = InputImage.fromBitmap(bmp, 0)
                                faceDetectorManager.detectFaces(
                                    inputImage = inputImage,
                                    onSuccess = { detectedFaceMeshes, width, height ->
                                        capturedFaceMeshes = detectedFaceMeshes
                                        capturedImageWidth = width
                                        capturedImageHeight = height
                                        Log.d("FaceDetectionApp", "Manual detection: ${detectedFaceMeshes.size} faces found")
                                        if (detectedFaceMeshes.isNotEmpty()) {
                                            analysisResults = faceAnalyzer.analyze(detectedFaceMeshes.first())
                                            showAnalysisScreen = true
                                        }
                                    },
                                    onFailure = { e ->
                                        Log.e("FaceDetectionApp", "Manual face detection failed", e)
                                    }
                                )
                            }
                        }
                    ) {
                        Text("重新检测")
                    }
                }
            }
        }

        // 人脸检测覆盖层 (仅在相机预览时显示)
        if (showCamera) {
            FaceOverlay(faceMeshes = faceMeshes, imageWidth = imageWidth, imageHeight = imageHeight, isBackCamera = isBackCamera)
        }
    }
}
