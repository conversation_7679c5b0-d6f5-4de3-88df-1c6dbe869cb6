package com.wendy.face.model

import com.google.mlkit.vision.facemesh.FaceMeshPoint

/**
 * 面相十二宫定义
 * 存储了每个宫位对应的Face Mesh关键点索引
 */
object FacePalaces {

    // 为了方便代码可读性，我们为一些关键点定义常量
    object Landmarks {
        const val NOSE_TIP = 1
        const val FOREHEAD_CENTER = 10
        const val CHIN_TIP = 152
        // ... 可以根据需要添加更多
    }

    val palaceIndices = mapOf(
        // 1. 命宫 (Life)
        "命宫" to listOf(10, 151, 9, 8, 168, 6, 197, 195, 5),
        // 2. 财帛宫 (Wealth) - 整个鼻子
        "财帛宫" to listOf(
            1, 6, 168, 197, 195, 5, 4, // 鼻梁和山根
            98, 327, // 鼻翼
            2, 94, 324 // 鼻尖周围
            // 这是一个简化的表示，完整的鼻子需要更多点
        ),
        // 3. 兄弟宫 (Siblings) - 眉毛
        "兄弟宫" to listOf(
            // 左眉
            70, 63, 105, 66, 107, 55, 65, 52, 53, 46,
            // 右眉
            336, 296, 334, 293, 300, 285, 295, 282, 283, 276
        ),
        // 4. 夫妻宫 (Spouse) - 眼尾
        "夫妻宫" to listOf(
            // 左侧
            33, 133, 160, 159, 158, 144, 145, 153,
            // 右侧
            362, 263, 387, 386, 385, 373, 374, 380
        ),
        // 5. 子女宫 (Children) - 下眼睑
        "子女宫" to listOf(
            // 左侧
            144, 145, 159, 130, 247, 246, 161, 160,
            // 右侧
            373, 374, 386, 359, 467, 466, 388, 387
        ),
        // 6. 疾厄宫 (Health) - 鼻梁
        "疾厄宫" to listOf(6, 168, 197, 195, 129, 358),
        // 7. 迁移宫 (Travel) - 额角
        "迁移宫" to listOf(
            // 左侧
            103, 67, 109, 108, 69, 104,
            // 右侧
            338, 297, 332, 337, 299, 333
        ),
        // 8. 奴仆宫 (Subordinates) - 下巴
        "奴仆宫" to listOf(152, 176, 148, 150, 136, 172, 205, 206, 18, 365, 377, 379, 400, 397),
        // 9. 官禄宫 (Career) - 额头中央
        "官禄宫" to listOf(10, 151, 8, 9, 107, 336),
        // 10. 田宅宫 (Property) - 上眼睑
        "田宅宫" to listOf(
            // 左侧 (眉毛下方, 眼睛上方)
            55, 65, 52, 190, 244,
            // 右侧
            285, 295, 282, 414, 464
        ),
        // 11. 福德宫 (Fortune) - 眉上
        "福德宫" to listOf(
            // 左侧
            70, 63, 105, 66, 107,
            // 右侧
            336, 296, 334, 293, 300
        ),
        // 12. 父母宫 (Parents) - 日月角
        "父母宫" to listOf(
            // 左侧
            67, 109, 10,
            // 右侧
            297, 338, 10
        )
    )

    /**
     * 获取特定宫位的所有关键点坐标
     * @param palaceName 宫位名称
     * @param allPoints 所有468个面部关键点
     * @return 该宫位对应的FaceMeshPoint列表
     */
    fun getPalacePoints(palaceName: String, allPoints: List<FaceMeshPoint>): List<FaceMeshPoint> {
        val indices = palaceIndices[palaceName] ?: return emptyList()
        return indices.map { allPoints[it] }
    }
}