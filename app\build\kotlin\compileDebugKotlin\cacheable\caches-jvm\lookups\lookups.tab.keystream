  Manifest android  CAMERA android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  SuppressLint android.annotation  Activity android.app  ActivityResultContracts android.app.Activity  Boolean android.app.Activity  Build android.app.Activity  
ContextCompat android.app.Activity  	Exception android.app.Activity  FaceDetectionApp android.app.Activity  	FaceTheme android.app.Activity  Log android.app.Activity  Manifest android.app.Activity  PackageManager android.app.Activity  PermissionUtils android.app.Activity  SimpleCameraPreview android.app.Activity  Suppress android.app.Activity  View android.app.Activity  WindowCompat android.app.Activity  WindowInsets android.app.Activity  WindowInsetsController android.app.Activity  
WindowManager android.app.Activity  getPermissionsToRequest android.app.Activity  
isNotEmpty android.app.Activity  let android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  
ContentValues android.content  Context android.content  openInputStream android.content.ContentResolver  android android.content.ContentValues  apply android.content.ContentValues  put android.content.ContentValues  ActivityResultContracts android.content.Context  Boolean android.content.Context  Build android.content.Context  
ContextCompat android.content.Context  	Exception android.content.Context  FaceDetectionApp android.content.Context  	FaceTheme android.content.Context  Log android.content.Context  Manifest android.content.Context  PackageManager android.content.Context  PermissionUtils android.content.Context  SimpleCameraPreview android.content.Context  Suppress android.content.Context  View android.content.Context  WindowCompat android.content.Context  WindowInsets android.content.Context  WindowInsetsController android.content.Context  
WindowManager android.content.Context  contentResolver android.content.Context  getPermissionsToRequest android.content.Context  
isNotEmpty android.content.Context  let android.content.Context  
setContent android.content.Context  ActivityResultContracts android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  	Exception android.content.ContextWrapper  FaceDetectionApp android.content.ContextWrapper  	FaceTheme android.content.ContextWrapper  Log android.content.ContextWrapper  Manifest android.content.ContextWrapper  PackageManager android.content.ContextWrapper  PermissionUtils android.content.ContextWrapper  SimpleCameraPreview android.content.ContextWrapper  Suppress android.content.ContextWrapper  View android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  WindowInsets android.content.ContextWrapper  WindowInsetsController android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  getPermissionsToRequest android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  let android.content.ContextWrapper  
setContent android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bitmap android.graphics  
BitmapFactory android.graphics  Canvas android.graphics  Matrix android.graphics  Paint android.graphics  Rect android.graphics  Config android.graphics.Bitmap  
asImageBitmap android.graphics.Bitmap  createBitmap android.graphics.Bitmap  
eraseColor android.graphics.Bitmap  height android.graphics.Bitmap  let android.graphics.Bitmap  recycle android.graphics.Bitmap  width android.graphics.Bitmap  	ARGB_8888 android.graphics.Bitmap.Config  decodeStream android.graphics.BitmapFactory  
drawCircle android.graphics.Canvas  drawRect android.graphics.Canvas  BLUE android.graphics.Color  RED android.graphics.Color  YELLOW android.graphics.Color  apply android.graphics.Matrix  
postRotate android.graphics.Matrix  	postScale android.graphics.Matrix  Style android.graphics.Paint  android android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  bottom android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  
ExifInterface 
android.media  Image 
android.media  ORIENTATION_NORMAL android.media.ExifInterface  ORIENTATION_ROTATE_180 android.media.ExifInterface  ORIENTATION_ROTATE_270 android.media.ExifInterface  ORIENTATION_ROTATE_90 android.media.ExifInterface  TAG_ORIENTATION android.media.ExifInterface  getAttributeInt android.media.ExifInterface  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  
RELATIVE_PATH (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  View android.view  WindowInsets android.view  WindowInsetsController android.view  
WindowManager android.view  ActivityResultContracts  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  FaceDetectionApp  android.view.ContextThemeWrapper  	FaceTheme  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  PermissionUtils  android.view.ContextThemeWrapper  SimpleCameraPreview  android.view.ContextThemeWrapper  Suppress  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  WindowInsets  android.view.ContextThemeWrapper  WindowInsetsController  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  getPermissionsToRequest  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  SYSTEM_UI_FLAG_FULLSCREEN android.view.View  SYSTEM_UI_FLAG_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_IMMERSIVE_STICKY android.view.View   SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN android.view.View  %SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_LAYOUT_STABLE android.view.View  systemUiVisibility android.view.View  addFlags android.view.Window  	decorView android.view.Window  insetsController android.view.Window  navigationBars android.view.WindowInsets.Type  
statusBars android.view.WindowInsets.Type  %BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE #android.view.WindowInsetsController  hide #android.view.WindowInsetsController  let #android.view.WindowInsetsController  systemBarsBehavior #android.view.WindowInsetsController  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  ComponentActivity androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  FaceDetectionApp #androidx.activity.ComponentActivity  	FaceTheme #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  PermissionUtils #androidx.activity.ComponentActivity  SimpleCameraPreview #androidx.activity.ComponentActivity  Suppress #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  WindowInsets #androidx.activity.ComponentActivity  WindowInsetsController #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  getPermissionsToRequest #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  Bitmap androidx.camera.core  Boolean androidx.camera.core  Camera androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  Context androidx.camera.core  
ContextCompat androidx.camera.core  	Exception androidx.camera.core  	Executors androidx.camera.core  FaceDetectorManager androidx.camera.core  FaceMesh androidx.camera.core  
ImageAnalysis androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  
ImageUtils androidx.camera.core  
InputImage androidx.camera.core  Int androidx.camera.core  LifecycleOwner androidx.camera.core  List androidx.camera.core  Locale androidx.camera.core  Log androidx.camera.core  Matrix androidx.camera.core  Preview androidx.camera.core  PreviewView androidx.camera.core  ProcessCameraProvider androidx.camera.core  SimpleDateFormat androidx.camera.core  SuppressLint androidx.camera.core  System androidx.camera.core  TAG androidx.camera.core  Unit androidx.camera.core  Uri androidx.camera.core  also androidx.camera.core  android androidx.camera.core  apply androidx.camera.core  context androidx.camera.core  flipBitmapHorizontal androidx.camera.core  let androidx.camera.core  loadAndRotateBitmap androidx.camera.core  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  also "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  <SAM-CONSTRUCTOR> +androidx.camera.core.ImageAnalysis.Analyzer  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  Builder !androidx.camera.core.ImageCapture  CAPTURE_MODE_MAXIMIZE_QUALITY !androidx.camera.core.ImageCapture  OnImageSavedCallback !androidx.camera.core.ImageCapture  OutputFileOptions !androidx.camera.core.ImageCapture  OutputFileResults !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setCaptureMode )androidx.camera.core.ImageCapture.Builder  Builder 3androidx.camera.core.ImageCapture.OutputFileOptions  build ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  savedUri 3androidx.camera.core.ImageCapture.OutputFileResults  message *androidx.camera.core.ImageCaptureException  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  PreviewView androidx.camera.view  surfaceProvider  androidx.camera.view.PreviewView  Canvas androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AnalysisResultCard "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Bitmap "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  BoxWithConstraints "androidx.compose.foundation.layout  BoxWithConstraintsScope "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  
CameraView "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  FaceAnalysisScreen "androidx.compose.foundation.layout  FaceDetectionApp "androidx.compose.foundation.layout  FaceMesh "androidx.compose.foundation.layout  FaceOverlay "androidx.compose.foundation.layout  	FaceTheme "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  
InputImage "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Log "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  PalaceAnalysisResult "androidx.compose.foundation.layout  PermissionUtils "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Suppress "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  View "androidx.compose.foundation.layout  WindowCompat "androidx.compose.foundation.layout  WindowInsets "androidx.compose.foundation.layout  WindowInsetsController "androidx.compose.foundation.layout  
WindowManager "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  
asImageBitmap "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  first "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  getPermissionsToRequest "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  radialGradient "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  shadow "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Start .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AnalysisResultCard +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Brush +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  CameraControls +androidx.compose.foundation.layout.BoxScope  CameraSelector +androidx.compose.foundation.layout.BoxScope  
CameraView +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  
ContextCompat +androidx.compose.foundation.layout.BoxScope  FaceAnalysisScreen +androidx.compose.foundation.layout.BoxScope  FaceDetectionGuide +androidx.compose.foundation.layout.BoxScope  FaceOverlay +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  
InputImage +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  Log +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Preview +androidx.compose.foundation.layout.BoxScope  PreviewView +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  also +androidx.compose.foundation.layout.BoxScope  
asImageBitmap +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  	emptyList +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  first +androidx.compose.foundation.layout.BoxScope  heightIn +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  items +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  	Alignment :androidx.compose.foundation.layout.BoxWithConstraintsScope  Canvas :androidx.compose.foundation.layout.BoxWithConstraintsScope  Color :androidx.compose.foundation.layout.BoxWithConstraintsScope  
FontWeight :androidx.compose.foundation.layout.BoxWithConstraintsScope  
MaterialTheme :androidx.compose.foundation.layout.BoxWithConstraintsScope  Modifier :androidx.compose.foundation.layout.BoxWithConstraintsScope  Offset :androidx.compose.foundation.layout.BoxWithConstraintsScope  Stroke :androidx.compose.foundation.layout.BoxWithConstraintsScope  Text :androidx.compose.foundation.layout.BoxWithConstraintsScope  	TextAlign :androidx.compose.foundation.layout.BoxWithConstraintsScope  align :androidx.compose.foundation.layout.BoxWithConstraintsScope  androidx :androidx.compose.foundation.layout.BoxWithConstraintsScope  center :androidx.compose.foundation.layout.BoxWithConstraintsScope  dp :androidx.compose.foundation.layout.BoxWithConstraintsScope  fillMaxSize :androidx.compose.foundation.layout.BoxWithConstraintsScope  	maxHeight :androidx.compose.foundation.layout.BoxWithConstraintsScope  maxWidth :androidx.compose.foundation.layout.BoxWithConstraintsScope  padding :androidx.compose.foundation.layout.BoxWithConstraintsScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  AnalysisResultCard .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Brush .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  FloatingActionButton .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Log .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  radialGradient .androidx.compose.foundation.layout.ColumnScope  shadow .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Brush +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  FloatingActionButton +androidx.compose.foundation.layout.RowScope  Log +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  radialGradient +androidx.compose.foundation.layout.RowScope  shadow +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  AnalysisResultCard .androidx.compose.foundation.lazy.LazyItemScope  AnalysisResultCard .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Brush androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CircleShape androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  FloatingActionButton androidx.compose.material3  Log androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Row androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  
background androidx.compose.material3  
cardColors androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  padding androidx.compose.material3  radialGradient androidx.compose.material3  shadow androidx.compose.material3  size androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  AndroidView androidx.compose.runtime  Arrangement androidx.compose.runtime  Bitmap androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Build androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  CameraControls androidx.compose.runtime  CameraSelector androidx.compose.runtime  
CameraView androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  
ContextCompat androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  	Exception androidx.compose.runtime  FaceAnalysisScreen androidx.compose.runtime  FaceDetectionApp androidx.compose.runtime  FaceDetectionGuide androidx.compose.runtime  FaceMesh androidx.compose.runtime  FaceOverlay androidx.compose.runtime  	FaceTheme androidx.compose.runtime  Image androidx.compose.runtime  
InputImage androidx.compose.runtime  Int androidx.compose.runtime  List androidx.compose.runtime  Log androidx.compose.runtime  Manifest androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  PackageManager androidx.compose.runtime  PalaceAnalysisResult androidx.compose.runtime  PermissionUtils androidx.compose.runtime  Preview androidx.compose.runtime  PreviewView androidx.compose.runtime  ProcessCameraProvider androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  SimpleCameraPreview androidx.compose.runtime  Suppress androidx.compose.runtime  Text androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  View androidx.compose.runtime  WindowCompat androidx.compose.runtime  WindowInsets androidx.compose.runtime  WindowInsetsController androidx.compose.runtime  
WindowManager androidx.compose.runtime  align androidx.compose.runtime  also androidx.compose.runtime  
asImageBitmap androidx.compose.runtime  com androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  first androidx.compose.runtime  forEach androidx.compose.runtime  getPermissionsToRequest androidx.compose.runtime  getValue androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  spacedBy androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  	onDispose .androidx.compose.runtime.DisposableEffectScope  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  	TopCenter androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	TopCenter 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  shadow androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  shadow androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  center androidx.compose.ui.geometry  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  center !androidx.compose.ui.geometry.Size  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  ImageBitmap androidx.compose.ui.graphics  	PointMode androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Black "androidx.compose.ui.graphics.Color  Blue "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Cyan "androidx.compose.ui.graphics.Color  Magenta "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Blue ,androidx.compose.ui.graphics.Color.Companion  Cyan ,androidx.compose.ui.graphics.Color.Companion  Magenta ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  	Companion &androidx.compose.ui.graphics.PointMode  Points &androidx.compose.ui.graphics.PointMode  Points 0androidx.compose.ui.graphics.PointMode.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  	PointMode 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  center 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  drawArc 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFaceMeshPoints 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawPoints 0androidx.compose.ui.graphics.drawscope.DrawScope  map 0androidx.compose.ui.graphics.drawscope.DrawScope  min 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Fit 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  Fit 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  toPx  androidx.compose.ui.unit.Density  div androidx.compose.ui.unit.Dp  minus androidx.compose.ui.unit.Dp  times androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  AndroidView androidx.compose.ui.viewinterop  ActivityResultContracts #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  FaceDetectionApp #androidx.core.app.ComponentActivity  	FaceTheme #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  PermissionUtils #androidx.core.app.ComponentActivity  SimpleCameraPreview #androidx.core.app.ComponentActivity  Suppress #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  WindowInsets #androidx.core.app.ComponentActivity  WindowInsetsController #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  getPermissionsToRequest #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  WindowCompat androidx.core.view  setDecorFitsSystemWindows androidx.core.view.WindowCompat  LifecycleOwner androidx.lifecycle  LocalLifecycleOwner androidx.lifecycle.compose  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  
InputImage com.google.mlkit.vision.common  
fromBitmap )com.google.mlkit.vision.common.InputImage  fromMediaImage )com.google.mlkit.vision.common.InputImage  height )com.google.mlkit.vision.common.InputImage  width )com.google.mlkit.vision.common.InputImage  x 'com.google.mlkit.vision.common.PointF3D  y 'com.google.mlkit.vision.common.PointF3D  z 'com.google.mlkit.vision.common.PointF3D  FaceMesh  com.google.mlkit.vision.facemesh  FaceMeshDetection  com.google.mlkit.vision.facemesh  FaceMeshDetector  com.google.mlkit.vision.facemesh  FaceMeshDetectorOptions  com.google.mlkit.vision.facemesh  
FaceMeshPoint  com.google.mlkit.vision.facemesh  	allPoints )com.google.mlkit.vision.facemesh.FaceMesh  	getClient 2com.google.mlkit.vision.facemesh.FaceMeshDetection  close 1com.google.mlkit.vision.facemesh.FaceMeshDetector  process 1com.google.mlkit.vision.facemesh.FaceMeshDetector  Builder 8com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions  	FACE_MESH 8com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions  build @com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions.Builder  
setUseCase @com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions.Builder  position .com.google.mlkit.vision.facemesh.FaceMeshPoint  ActivityResultContracts com.wendy.face  	Alignment com.wendy.face  AndroidView com.wendy.face  Arrangement com.wendy.face  Bitmap com.wendy.face  Boolean com.wendy.face  Box com.wendy.face  Build com.wendy.face  Bundle com.wendy.face  Button com.wendy.face  CameraSelector com.wendy.face  
CameraView com.wendy.face  Column com.wendy.face  ComponentActivity com.wendy.face  
Composable com.wendy.face  ContentScale com.wendy.face  
ContextCompat com.wendy.face  	Exception com.wendy.face  FaceAnalysisScreen com.wendy.face  FaceDetectionApp com.wendy.face  FaceMesh com.wendy.face  FaceOverlay com.wendy.face  	FaceTheme com.wendy.face  Image com.wendy.face  
InputImage com.wendy.face  List com.wendy.face  Log com.wendy.face  MainActivity com.wendy.face  Manifest com.wendy.face  Modifier com.wendy.face  PackageManager com.wendy.face  PalaceAnalysisResult com.wendy.face  PermissionUtils com.wendy.face  Preview com.wendy.face  PreviewView com.wendy.face  ProcessCameraProvider com.wendy.face  SimpleCameraPreview com.wendy.face  Suppress com.wendy.face  TestCameraActivity com.wendy.face  Text com.wendy.face  Uri com.wendy.face  View com.wendy.face  WindowCompat com.wendy.face  WindowInsets com.wendy.face  WindowInsetsController com.wendy.face  
WindowManager com.wendy.face  align com.wendy.face  also com.wendy.face  
asImageBitmap com.wendy.face  com com.wendy.face  	emptyList com.wendy.face  fillMaxSize com.wendy.face  first com.wendy.face  forEach com.wendy.face  getPermissionsToRequest com.wendy.face  getValue com.wendy.face  
isNotEmpty com.wendy.face  let com.wendy.face  mutableStateOf com.wendy.face  padding com.wendy.face  provideDelegate com.wendy.face  remember com.wendy.face  setValue com.wendy.face  spacedBy com.wendy.face  ActivityResultContracts com.wendy.face.MainActivity  Build com.wendy.face.MainActivity  FaceDetectionApp com.wendy.face.MainActivity  	FaceTheme com.wendy.face.MainActivity  Log com.wendy.face.MainActivity  PermissionUtils com.wendy.face.MainActivity  View com.wendy.face.MainActivity  WindowCompat com.wendy.face.MainActivity  WindowInsets com.wendy.face.MainActivity  WindowInsetsController com.wendy.face.MainActivity  
WindowManager com.wendy.face.MainActivity  getPermissionsToRequest com.wendy.face.MainActivity  
isNotEmpty com.wendy.face.MainActivity  let com.wendy.face.MainActivity  registerForActivityResult com.wendy.face.MainActivity  "requestMultiplePermissionsLauncher com.wendy.face.MainActivity  
setContent com.wendy.face.MainActivity  setupFullScreenMode com.wendy.face.MainActivity  window com.wendy.face.MainActivity  ActivityResultContracts !com.wendy.face.TestCameraActivity  
ContextCompat !com.wendy.face.TestCameraActivity  	FaceTheme !com.wendy.face.TestCameraActivity  Log !com.wendy.face.TestCameraActivity  Manifest !com.wendy.face.TestCameraActivity  PackageManager !com.wendy.face.TestCameraActivity  SimpleCameraPreview !com.wendy.face.TestCameraActivity  registerForActivityResult !com.wendy.face.TestCameraActivity  requestPermissionLauncher !com.wendy.face.TestCameraActivity  
setContent !com.wendy.face.TestCameraActivity  Double com.wendy.face.analyzer  FaceAnalyzer com.wendy.face.analyzer  FaceMesh com.wendy.face.analyzer  
FaceMeshPoint com.wendy.face.analyzer  FacePalaces com.wendy.face.analyzer  List com.wendy.face.analyzer  PalaceAnalysisResult com.wendy.face.analyzer  String com.wendy.face.analyzer  abs com.wendy.face.analyzer  average com.wendy.face.analyzer  forEach com.wendy.face.analyzer  getPalacePoints com.wendy.face.analyzer  indices com.wendy.face.analyzer  
isNotEmpty com.wendy.face.analyzer  map com.wendy.face.analyzer  
mutableListOf com.wendy.face.analyzer  
plusAssign com.wendy.face.analyzer  FacePalaces $com.wendy.face.analyzer.FaceAnalyzer  PalaceAnalysisResult $com.wendy.face.analyzer.FaceAnalyzer  abs $com.wendy.face.analyzer.FaceAnalyzer  analyze $com.wendy.face.analyzer.FaceAnalyzer  average $com.wendy.face.analyzer.FaceAnalyzer  
calculateArea $com.wendy.face.analyzer.FaceAnalyzer  calculateFullness $com.wendy.face.analyzer.FaceAnalyzer  generateDescription $com.wendy.face.analyzer.FaceAnalyzer  getPalacePoints $com.wendy.face.analyzer.FaceAnalyzer  indices $com.wendy.face.analyzer.FaceAnalyzer  
isNotEmpty $com.wendy.face.analyzer.FaceAnalyzer  map $com.wendy.face.analyzer.FaceAnalyzer  
mutableListOf $com.wendy.face.analyzer.FaceAnalyzer  
plusAssign $com.wendy.face.analyzer.FaceAnalyzer  area ,com.wendy.face.analyzer.PalaceAnalysisResult  description ,com.wendy.face.analyzer.PalaceAnalysisResult  fullness ,com.wendy.face.analyzer.PalaceAnalysisResult  
palaceName ,com.wendy.face.analyzer.PalaceAnalysisResult  Bitmap com.wendy.face.camera  Boolean com.wendy.face.camera  
CameraManager com.wendy.face.camera  CameraSelector com.wendy.face.camera  Context com.wendy.face.camera  
ContextCompat com.wendy.face.camera  	Exception com.wendy.face.camera  	Executors com.wendy.face.camera  FaceDetectorManager com.wendy.face.camera  FaceMesh com.wendy.face.camera  
ImageAnalysis com.wendy.face.camera  ImageCapture com.wendy.face.camera  ImageCaptureException com.wendy.face.camera  
ImageUtils com.wendy.face.camera  
InputImage com.wendy.face.camera  Int com.wendy.face.camera  LifecycleOwner com.wendy.face.camera  List com.wendy.face.camera  Locale com.wendy.face.camera  Log com.wendy.face.camera  Matrix com.wendy.face.camera  Preview com.wendy.face.camera  PreviewView com.wendy.face.camera  ProcessCameraProvider com.wendy.face.camera  SimpleDateFormat com.wendy.face.camera  SuppressLint com.wendy.face.camera  System com.wendy.face.camera  TAG com.wendy.face.camera  Unit com.wendy.face.camera  Uri com.wendy.face.camera  also com.wendy.face.camera  android com.wendy.face.camera  apply com.wendy.face.camera  context com.wendy.face.camera  flipBitmapHorizontal com.wendy.face.camera  let com.wendy.face.camera  loadAndRotateBitmap com.wendy.face.camera  Bitmap #com.wendy.face.camera.CameraManager  Boolean #com.wendy.face.camera.CameraManager  CameraSelector #com.wendy.face.camera.CameraManager  Context #com.wendy.face.camera.CameraManager  
ContextCompat #com.wendy.face.camera.CameraManager  	Exception #com.wendy.face.camera.CameraManager  	Executors #com.wendy.face.camera.CameraManager  FaceDetectorManager #com.wendy.face.camera.CameraManager  FaceMesh #com.wendy.face.camera.CameraManager  
ImageAnalysis #com.wendy.face.camera.CameraManager  ImageCapture #com.wendy.face.camera.CameraManager  ImageCaptureException #com.wendy.face.camera.CameraManager  
ImageUtils #com.wendy.face.camera.CameraManager  
InputImage #com.wendy.face.camera.CameraManager  Int #com.wendy.face.camera.CameraManager  LifecycleOwner #com.wendy.face.camera.CameraManager  List #com.wendy.face.camera.CameraManager  Locale #com.wendy.face.camera.CameraManager  Log #com.wendy.face.camera.CameraManager  Matrix #com.wendy.face.camera.CameraManager  Preview #com.wendy.face.camera.CameraManager  PreviewView #com.wendy.face.camera.CameraManager  ProcessCameraProvider #com.wendy.face.camera.CameraManager  SimpleDateFormat #com.wendy.face.camera.CameraManager  SuppressLint #com.wendy.face.camera.CameraManager  System #com.wendy.face.camera.CameraManager  TAG #com.wendy.face.camera.CameraManager  Unit #com.wendy.face.camera.CameraManager  Uri #com.wendy.face.camera.CameraManager  also #com.wendy.face.camera.CameraManager  android #com.wendy.face.camera.CameraManager  apply #com.wendy.face.camera.CameraManager  
bindCamera #com.wendy.face.camera.CameraManager  cameraExecutor #com.wendy.face.camera.CameraManager  cameraProvider #com.wendy.face.camera.CameraManager  cameraProviderFuture #com.wendy.face.camera.CameraManager  context #com.wendy.face.camera.CameraManager  faceDetectorManager #com.wendy.face.camera.CameraManager  flipBitmapHorizontal #com.wendy.face.camera.CameraManager  imageCapture #com.wendy.face.camera.CameraManager  let #com.wendy.face.camera.CameraManager  lifecycleOwner #com.wendy.face.camera.CameraManager  loadAndRotateBitmap #com.wendy.face.camera.CameraManager  release #com.wendy.face.camera.CameraManager  takePicture #com.wendy.face.camera.CameraManager  Bitmap -com.wendy.face.camera.CameraManager.Companion  CameraSelector -com.wendy.face.camera.CameraManager.Companion  
ContextCompat -com.wendy.face.camera.CameraManager.Companion  	Executors -com.wendy.face.camera.CameraManager.Companion  FaceDetectorManager -com.wendy.face.camera.CameraManager.Companion  
ImageAnalysis -com.wendy.face.camera.CameraManager.Companion  ImageCapture -com.wendy.face.camera.CameraManager.Companion  
ImageUtils -com.wendy.face.camera.CameraManager.Companion  
InputImage -com.wendy.face.camera.CameraManager.Companion  Locale -com.wendy.face.camera.CameraManager.Companion  Log -com.wendy.face.camera.CameraManager.Companion  Matrix -com.wendy.face.camera.CameraManager.Companion  Preview -com.wendy.face.camera.CameraManager.Companion  ProcessCameraProvider -com.wendy.face.camera.CameraManager.Companion  SimpleDateFormat -com.wendy.face.camera.CameraManager.Companion  System -com.wendy.face.camera.CameraManager.Companion  TAG -com.wendy.face.camera.CameraManager.Companion  also -com.wendy.face.camera.CameraManager.Companion  android -com.wendy.face.camera.CameraManager.Companion  apply -com.wendy.face.camera.CameraManager.Companion  context -com.wendy.face.camera.CameraManager.Companion  flipBitmapHorizontal -com.wendy.face.camera.CameraManager.Companion  let -com.wendy.face.camera.CameraManager.Companion  loadAndRotateBitmap -com.wendy.face.camera.CameraManager.Companion  OnImageSavedCallback 0com.wendy.face.camera.CameraManager.ImageCapture  OutputFileResults 0com.wendy.face.camera.CameraManager.ImageCapture  OnImageSavedCallback "com.wendy.face.camera.ImageCapture  OutputFileResults "com.wendy.face.camera.ImageCapture  Boolean com.wendy.face.detection  Color com.wendy.face.detection  	DrawScope com.wendy.face.detection  	Exception com.wendy.face.detection  FaceDetectorManager com.wendy.face.detection  FaceMesh com.wendy.face.detection  FaceMeshDetection com.wendy.face.detection  FaceMeshDetectorOptions com.wendy.face.detection  FaceRenderer com.wendy.face.detection  Float com.wendy.face.detection  
InputImage com.wendy.face.detection  Int com.wendy.face.detection  List com.wendy.face.detection  Log com.wendy.face.detection  Offset com.wendy.face.detection  TAG com.wendy.face.detection  Unit com.wendy.face.detection  forEach com.wendy.face.detection  forEachIndexed com.wendy.face.detection  with com.wendy.face.detection  	Exception ,com.wendy.face.detection.FaceDetectorManager  FaceMesh ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetection ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetectorOptions ,com.wendy.face.detection.FaceDetectorManager  
InputImage ,com.wendy.face.detection.FaceDetectorManager  Int ,com.wendy.face.detection.FaceDetectorManager  List ,com.wendy.face.detection.FaceDetectorManager  Log ,com.wendy.face.detection.FaceDetectorManager  TAG ,com.wendy.face.detection.FaceDetectorManager  Unit ,com.wendy.face.detection.FaceDetectorManager  detectFaces ,com.wendy.face.detection.FaceDetectorManager  faceMeshDetector ,com.wendy.face.detection.FaceDetectorManager  faceMeshDetectorOptions ,com.wendy.face.detection.FaceDetectorManager  forEachIndexed ,com.wendy.face.detection.FaceDetectorManager  release ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetection 6com.wendy.face.detection.FaceDetectorManager.Companion  FaceMeshDetectorOptions 6com.wendy.face.detection.FaceDetectorManager.Companion  Log 6com.wendy.face.detection.FaceDetectorManager.Companion  TAG 6com.wendy.face.detection.FaceDetectorManager.Companion  forEachIndexed 6com.wendy.face.detection.FaceDetectorManager.Companion  Color %com.wendy.face.detection.FaceRenderer  Offset %com.wendy.face.detection.FaceRenderer  dp %com.wendy.face.detection.FaceRenderer  drawFaceMeshPoints %com.wendy.face.detection.FaceRenderer  with %com.wendy.face.detection.FaceRenderer  
FaceMeshPoint com.wendy.face.model  FacePalaces com.wendy.face.model  List com.wendy.face.model  String com.wendy.face.model  	emptyList com.wendy.face.model  listOf com.wendy.face.model  map com.wendy.face.model  mapOf com.wendy.face.model  to com.wendy.face.model  
FaceMeshPoint  com.wendy.face.model.FacePalaces  List  com.wendy.face.model.FacePalaces  String  com.wendy.face.model.FacePalaces  	emptyList  com.wendy.face.model.FacePalaces  getPalacePoints  com.wendy.face.model.FacePalaces  listOf  com.wendy.face.model.FacePalaces  map  com.wendy.face.model.FacePalaces  mapOf  com.wendy.face.model.FacePalaces  
palaceIndices  com.wendy.face.model.FacePalaces  to  com.wendy.face.model.FacePalaces  	Alignment com.wendy.face.ui.components  AndroidView com.wendy.face.ui.components  Arrangement com.wendy.face.ui.components  Bitmap com.wendy.face.ui.components  Boolean com.wendy.face.ui.components  Box com.wendy.face.ui.components  Brush com.wendy.face.ui.components  CameraControls com.wendy.face.ui.components  
CameraView com.wendy.face.ui.components  Canvas com.wendy.face.ui.components  Card com.wendy.face.ui.components  CardDefaults com.wendy.face.ui.components  CircleShape com.wendy.face.ui.components  Color com.wendy.face.ui.components  
Composable com.wendy.face.ui.components  DisposableEffect com.wendy.face.ui.components  FaceDetectionGuide com.wendy.face.ui.components  FaceMesh com.wendy.face.ui.components  FaceOverlay com.wendy.face.ui.components  FloatingActionButton com.wendy.face.ui.components  
FontWeight com.wendy.face.ui.components  Int com.wendy.face.ui.components  List com.wendy.face.ui.components  Log com.wendy.face.ui.components  
MaterialTheme com.wendy.face.ui.components  Modifier com.wendy.face.ui.components  Offset com.wendy.face.ui.components  	PointMode com.wendy.face.ui.components  PreviewView com.wendy.face.ui.components  Row com.wendy.face.ui.components  Stroke com.wendy.face.ui.components  Text com.wendy.face.ui.components  	TextAlign com.wendy.face.ui.components  Unit com.wendy.face.ui.components  Uri com.wendy.face.ui.components  align com.wendy.face.ui.components  androidx com.wendy.face.ui.components  
background com.wendy.face.ui.components  
cardColors com.wendy.face.ui.components  fillMaxSize com.wendy.face.ui.components  fillMaxWidth com.wendy.face.ui.components  forEach com.wendy.face.ui.components  getValue com.wendy.face.ui.components  listOf com.wendy.face.ui.components  map com.wendy.face.ui.components  min com.wendy.face.ui.components  mutableStateOf com.wendy.face.ui.components  padding com.wendy.face.ui.components  provideDelegate com.wendy.face.ui.components  radialGradient com.wendy.face.ui.components  remember com.wendy.face.ui.components  setValue com.wendy.face.ui.components  shadow com.wendy.face.ui.components  size com.wendy.face.ui.components  AnalysisResultCard com.wendy.face.ui.screens  Arrangement com.wendy.face.ui.screens  Bitmap com.wendy.face.ui.screens  Box com.wendy.face.ui.screens  Brush com.wendy.face.ui.screens  Button com.wendy.face.ui.screens  CardDefaults com.wendy.face.ui.screens  Color com.wendy.face.ui.screens  Column com.wendy.face.ui.screens  
Composable com.wendy.face.ui.screens  ContentScale com.wendy.face.ui.screens  FaceAnalysisScreen com.wendy.face.ui.screens  FaceMesh com.wendy.face.ui.screens  FaceOverlay com.wendy.face.ui.screens  
FontWeight com.wendy.face.ui.screens  Image com.wendy.face.ui.screens  
LazyColumn com.wendy.face.ui.screens  List com.wendy.face.ui.screens  
MaterialTheme com.wendy.face.ui.screens  Modifier com.wendy.face.ui.screens  PalaceAnalysisResult com.wendy.face.ui.screens  Row com.wendy.face.ui.screens  Spacer com.wendy.face.ui.screens  Text com.wendy.face.ui.screens  Unit com.wendy.face.ui.screens  
asImageBitmap com.wendy.face.ui.screens  
background com.wendy.face.ui.screens  
cardColors com.wendy.face.ui.screens  
cardElevation com.wendy.face.ui.screens  fillMaxSize com.wendy.face.ui.screens  fillMaxWidth com.wendy.face.ui.screens  format com.wendy.face.ui.screens  height com.wendy.face.ui.screens  heightIn com.wendy.face.ui.screens  listOf com.wendy.face.ui.screens  padding com.wendy.face.ui.screens  verticalGradient com.wendy.face.ui.screens  weight com.wendy.face.ui.screens  Boolean com.wendy.face.ui.theme  Build com.wendy.face.ui.theme  
Composable com.wendy.face.ui.theme  DarkColorScheme com.wendy.face.ui.theme  	FaceTheme com.wendy.face.ui.theme  
FontFamily com.wendy.face.ui.theme  
FontWeight com.wendy.face.ui.theme  LightColorScheme com.wendy.face.ui.theme  Pink40 com.wendy.face.ui.theme  Pink80 com.wendy.face.ui.theme  Purple40 com.wendy.face.ui.theme  Purple80 com.wendy.face.ui.theme  PurpleGrey40 com.wendy.face.ui.theme  PurpleGrey80 com.wendy.face.ui.theme  
Typography com.wendy.face.ui.theme  Unit com.wendy.face.ui.theme  Array com.wendy.face.utils  Bitmap com.wendy.face.utils  
BitmapFactory com.wendy.face.utils  Boolean com.wendy.face.utils  Context com.wendy.face.utils  
ContextCompat com.wendy.face.utils  	Exception com.wendy.face.utils  
ExifInterface com.wendy.face.utils  Float com.wendy.face.utils  
ImageUtils com.wendy.face.utils  Int com.wendy.face.utils  List com.wendy.face.utils  Log com.wendy.face.utils  Manifest com.wendy.face.utils  Matrix com.wendy.face.utils  PackageManager com.wendy.face.utils  PermissionUtils com.wendy.face.utils  Rect com.wendy.face.utils  String com.wendy.face.utils  Uri com.wendy.face.utils  android com.wendy.face.utils  apply com.wendy.face.utils  arrayOf com.wendy.face.utils  first com.wendy.face.utils  max com.wendy.face.utils  min com.wendy.face.utils  
mutableListOf com.wendy.face.utils  toTypedArray com.wendy.face.utils  Bitmap com.wendy.face.utils.ImageUtils  
BitmapFactory com.wendy.face.utils.ImageUtils  
ExifInterface com.wendy.face.utils.ImageUtils  Log com.wendy.face.utils.ImageUtils  Matrix com.wendy.face.utils.ImageUtils  TAG com.wendy.face.utils.ImageUtils  android com.wendy.face.utils.ImageUtils  apply com.wendy.face.utils.ImageUtils  arrayOf com.wendy.face.utils.ImageUtils  first com.wendy.face.utils.ImageUtils  loadAndRotateBitmap com.wendy.face.utils.ImageUtils  max com.wendy.face.utils.ImageUtils  min com.wendy.face.utils.ImageUtils  
ContextCompat $com.wendy.face.utils.PermissionUtils  Manifest $com.wendy.face.utils.PermissionUtils  PackageManager $com.wendy.face.utils.PermissionUtils  android $com.wendy.face.utils.PermissionUtils  getPermissionsToRequest $com.wendy.face.utils.PermissionUtils  hasCameraPermission $com.wendy.face.utils.PermissionUtils  hasStoragePermission $com.wendy.face.utils.PermissionUtils  
mutableListOf $com.wendy.face.utils.PermissionUtils  toTypedArray $com.wendy.face.utils.PermissionUtils  InputStream java.io  close java.io.InputStream  	Exception 	java.lang  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Bitmap 	java.util  Boolean 	java.util  CameraSelector 	java.util  Context 	java.util  
ContextCompat 	java.util  	Exception 	java.util  	Executors 	java.util  FaceDetectorManager 	java.util  FaceMesh 	java.util  
ImageAnalysis 	java.util  ImageCapture 	java.util  ImageCaptureException 	java.util  
ImageUtils 	java.util  
InputImage 	java.util  Int 	java.util  LifecycleOwner 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  Matrix 	java.util  Preview 	java.util  PreviewView 	java.util  ProcessCameraProvider 	java.util  SimpleDateFormat 	java.util  SuppressLint 	java.util  System 	java.util  TAG 	java.util  Unit 	java.util  Uri 	java.util  also 	java.util  android 	java.util  apply 	java.util  context 	java.util  flipBitmapHorizontal 	java.util  let 	java.util  loadAndRotateBitmap 	java.util  OnImageSavedCallback java.util.ImageCapture  OutputFileResults java.util.ImageCapture  US java.util.Locale  Executor java.util.concurrent  	Executors java.util.concurrent  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  Array kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  Suppress kotlin  also kotlin  apply kotlin  arrayOf kotlin  let kotlin  map kotlin  to kotlin  with kotlin  
isNotEmpty kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  div 
kotlin.Double  minus 
kotlin.Double  plus 
kotlin.Double  
plusAssign 
kotlin.Double  sp 
kotlin.Double  times 
kotlin.Double  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  invoke kotlin.Function3  	compareTo 
kotlin.Int  div 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  format 
kotlin.String  to 
kotlin.String  message kotlin.Throwable  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  Set kotlin.collections  average kotlin.collections  	emptyList kotlin.collections  first kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  max kotlin.collections  min kotlin.collections  
mutableListOf kotlin.collections  
plusAssign kotlin.collections  toTypedArray kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  average kotlin.collections.List  first kotlin.collections.List  get kotlin.collections.List  indices kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  Entry kotlin.collections.Map  entries kotlin.collections.Map  get kotlin.collections.Map  keys kotlin.collections.Map  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  add kotlin.collections.MutableList  size kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  abs kotlin.math  cos kotlin.math  max kotlin.math  min kotlin.math  sin kotlin.math  IntRange 
kotlin.ranges  first 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  Sequence kotlin.sequences  average kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  map kotlin.sequences  max kotlin.sequences  min kotlin.sequences  first kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  indices kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  max kotlin.text  min kotlin.text  Face3DPointsDisplay +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  	BottomEnd androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  coerceAtMost 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFace3DPoints 0androidx.compose.ui.graphics.drawscope.DrawScope  boundingBox )com.google.mlkit.vision.facemesh.FaceMesh  	DrawScope com.wendy.face.ui.components  Face3DPointsDisplay com.wendy.face.ui.components  Float com.wendy.face.ui.components  coerceAtMost com.wendy.face.ui.components  drawFace3DPoints com.wendy.face.ui.components  first com.wendy.face.ui.components  
isNotEmpty com.wendy.face.ui.components  coerceAtMost kotlin.Float  coerceAtMost 
kotlin.ranges  currentIsBackCamera androidx.camera.core  Boolean "androidx.compose.foundation.layout  currentIsBackCamera com.wendy.face.camera  currentIsBackCamera #com.wendy.face.camera.CameraManager  currentIsBackCamera -com.wendy.face.camera.CameraManager.Companion  Boolean com.wendy.face.ui.screens  currentIsBackCamera 	java.util  Handler 
android.os  postDelayed android.os.Handler  
getMainLooper android.os.Looper  android "androidx.compose.foundation.layout  android +androidx.compose.foundation.layout.BoxScope  android androidx.compose.runtime  android com.wendy.face  android com.wendy.face.ui.components  
InputImage .androidx.compose.foundation.layout.ColumnScope  first .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  android 0androidx.compose.ui.graphics.drawscope.DrawScope  PreviewView  androidx.camera.view.PreviewView  	ScaleType  androidx.camera.view.PreviewView  apply  androidx.camera.view.PreviewView  	scaleType  androidx.camera.view.PreviewView  FILL_CENTER *androidx.camera.view.PreviewView.ScaleType  androidx "androidx.compose.foundation.layout  Canvas +androidx.compose.foundation.layout.BoxScope  Tuple4 +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  drawFacePoints +androidx.compose.foundation.layout.BoxScope  androidx androidx.compose.runtime  apply androidx.compose.runtime  
background androidx.compose.runtime  TopStart androidx.compose.ui.Alignment  TopStart 'androidx.compose.ui.Alignment.Companion  Green "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  Green ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  Tuple4 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFacePoints 0androidx.compose.ui.graphics.drawscope.DrawScope  drawRect 0androidx.compose.ui.graphics.drawscope.DrawScope  androidx com.wendy.face  
background com.wendy.face  T com.wendy.face.ui.components  Tuple4 com.wendy.face.ui.components  apply com.wendy.face.ui.components  drawFacePoints com.wendy.face.ui.components  
component1 #com.wendy.face.ui.components.Tuple4  
component2 #com.wendy.face.ui.components.Tuple4  
component3 #com.wendy.face.ui.components.Tuple4  
component4 #com.wendy.face.ui.components.Tuple4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     