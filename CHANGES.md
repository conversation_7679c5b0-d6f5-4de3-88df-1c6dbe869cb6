# 人脸检测应用修改说明

## 修改概述

本次修改解决了两个主要问题：
1. 拍照后跳转页面没有显示标注点
2. 添加了头部裁剪功能

## 详细修改内容

### 1. 修复拍照后标注点显示问题

**问题描述：**
- 拍照后跳转到照片显示页面时，人脸检测的标注点消失了
- 原因是在显示照片时，faces状态被清空，导致FaceOverlay组件无法显示标注点

**解决方案：**
在 `MainActivity.kt` 中添加了新的状态变量来保存拍照时的人脸检测结果：

```kotlin
var capturedFaces by remember { mutableStateOf<List<Face>>(emptyList()) }
var capturedImageWidth by remember { mutableStateOf(0) }
var capturedImageHeight by remember { mutableStateOf(0) }
```

**修改的文件：**
- `app/src/main/java/com/wendy/face/MainActivity.kt`

**关键修改点：**
1. 在拍照回调中保存人脸检测结果：
   ```kotlin
   onImageCaptured = { uri, bitmap ->
       capturedImageUri = uri
       capturedBitmap = bitmap
       // 保存拍照时的人脸检测结果
       capturedFaces = faces
       capturedImageWidth = imageWidth
       capturedImageHeight = imageHeight
       showCamera = false
   }
   ```

2. 在显示照片时使用保存的人脸检测结果：
   ```kotlin
   if (showCamera) {
       FaceOverlay(faces = faces, imageWidth = imageWidth, imageHeight = imageHeight)
   } else if (!showCroppedImage) {
       // 只在显示原图时显示人脸检测结果，裁剪后的图片不显示覆盖层
       FaceOverlay(faces = capturedFaces, imageWidth = capturedImageWidth, imageHeight = capturedImageHeight)
   }
   ```

### 2. 添加头部裁剪功能

**功能描述：**
- 根据人脸检测结果自动裁剪出头部区域
- 支持多人脸场景，会包含所有检测到的人脸
- 可配置扩展比例，在人脸周围留出更多空间

**新增功能：**
在 `ImageUtils.kt` 中添加了头部裁剪相关方法：

```kotlin
fun cropHeadRegion(bitmap: Bitmap, faces: List<Face>, expandRatio: Float = 0.3f): Bitmap
fun adjustFacesForCrop(faces: List<Face>, cropLeft: Int, cropTop: Int): List<Face>
```

**修改的文件：**
- `app/src/main/java/com/wendy/face/utils/ImageUtils.kt`
- `app/src/main/java/com/wendy/face/MainActivity.kt`

**UI改进：**
在照片显示页面添加了控制按钮：
- "裁剪头部" 按钮：执行头部裁剪
- "显示原图" 按钮：切换回原图显示
- "返回摄像头" 按钮：返回相机界面

### 3. 裁剪算法说明

**算法逻辑：**
1. 遍历所有检测到的人脸，找到包含所有人脸的最小矩形
2. 根据扩展比例（默认30%）在人脸区域周围添加边距
3. 确保裁剪区域不超出原图边界
4. 执行图片裁剪操作

**多人脸处理：**
- 当检测到多个人脸时，算法会计算包含所有人脸的最小边界框
- 然后在此基础上进行扩展和裁剪

### 4. 用户体验改进

**操作流程：**
1. 用户打开相机进行人脸检测
2. 拍照后自动跳转到照片显示页面，显示原图和人脸标注点
3. 如果检测到人脸，显示"裁剪头部"按钮
4. 点击"裁剪头部"后显示裁剪后的图片（不显示标注点）
5. 可以通过"显示原图"按钮切换回原图查看
6. 通过"返回摄像头"按钮返回拍照界面

**状态管理：**
- `showCamera`: 控制是否显示相机界面
- `showCroppedImage`: 控制是否显示裁剪后的图片
- `capturedFaces`: 保存拍照时的人脸检测结果
- `croppedBitmap`: 保存裁剪后的图片

### 5. 测试

添加了单元测试来验证核心功能：
- `app/src/test/java/com/wendy/face/utils/ImageUtilsTest.kt`

测试覆盖：
- 显示区域计算的正确性
- 不同图片宽高比的处理

## 技术要点

1. **状态管理**：使用Compose的remember和mutableStateOf来管理UI状态
2. **图片处理**：使用Android的Bitmap API进行图片裁剪
3. **坐标计算**：正确处理人脸检测坐标与图片显示坐标的映射关系
4. **边界检查**：确保裁剪区域不超出原图边界
5. **内存管理**：适当的Bitmap回收和状态清理

## 注意事项

1. 裁剪功能只在检测到人脸时才会显示
2. 裁剪后的图片不显示人脸标注点，以保持界面简洁
3. 所有状态在返回相机时都会被正确清理
4. 支持多人脸场景的智能裁剪
