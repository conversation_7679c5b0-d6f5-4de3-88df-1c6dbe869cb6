package com.wendy.face.camera

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.util.Log
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.detection.FaceDetectorManager
import com.wendy.face.utils.ImageUtils
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.Executors

/**
 * 相机管理器
 * 负责相机的初始化、配置和拍照功能
 */
class CameraManager(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner
) {
    companion object {
        private const val TAG = "CameraManager"
    }

    private var cameraProvider: ProcessCameraProvider? = null
    private var imageCapture: ImageCapture? = null
    private val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
    private val faceDetectorManager = FaceDetectorManager()
    private val cameraExecutor = Executors.newSingleThreadExecutor()
    private var currentIsBackCamera: Boolean = true

    /**
     * 绑定相机到PreviewView
     * @param previewView 预览视图
     * @param isBackCamera 是否使用后置摄像头
     * @param onFacesDetected 人脸网格检测回调
     */
    @SuppressLint("UnsafeOptInUsageError")
    fun bindCamera(
        previewView: PreviewView,
        isBackCamera: Boolean,
        onFacesDetected: ((List<FaceMesh>, Int, Int) -> Unit)? // 改为可选参数
    ) {
        Log.d(TAG, "bindCamera called, isBackCamera: $isBackCamera")
        currentIsBackCamera = isBackCamera // 记录当前摄像头类型
        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                Log.d(TAG, "CameraProvider obtained")

                val preview = Preview.Builder().build().also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }

                imageCapture = ImageCapture.Builder()
                    .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                    .build()

                // 只在需要人脸检测时创建图像分析器
                val imageAnalyzer = if (onFacesDetected != null) {
                    ImageAnalysis.Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build()
                        .also {
                            it.setAnalyzer(cameraExecutor) { imageProxy ->
                                val rotationDegrees = imageProxy.imageInfo.rotationDegrees
                                val image = imageProxy.image
                                if (image != null) {
                                    val inputImage = InputImage.fromMediaImage(image, rotationDegrees)
                                    faceDetectorManager.detectFaces(
                                        inputImage,
                                        onSuccess = { faceMeshes, width, height ->
                                            onFacesDetected(faceMeshes, width, height)
                                            imageProxy.close()
                                        },
                                        onFailure = { e ->
                                            Log.e(TAG, "Face mesh detection failed during preview", e)
                                            imageProxy.close()
                                        }
                                    )
                                } else {
                                    imageProxy.close()
                                }
                            }
                        }
                } else {
                    null // 预览阶段不启用人脸检测
                }

                val cameraSelector = if (isBackCamera) {
                    CameraSelector.DEFAULT_BACK_CAMERA
                } else {
                    CameraSelector.DEFAULT_FRONT_CAMERA
                }

                cameraProvider?.unbindAll()

                // 根据是否需要人脸检测来决定绑定的用例
                if (imageAnalyzer != null) {
                    cameraProvider?.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageCapture,
                        imageAnalyzer
                    )
                } else {
                    // 只绑定预览和拍照，不绑定图像分析
                    cameraProvider?.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageCapture
                    )
                }
                Log.d(TAG, "Camera bound successfully")
            } catch (exc: Exception) {
                Log.e(TAG, "Use case binding failed", exc)
            }
        }, ContextCompat.getMainExecutor(context))
    }

    /**
     * 拍照
     * @param onImageCaptured 图片捕获回调
     */
    fun takePicture(
        onImageCaptured: (Uri?, Bitmap?) -> Unit
    ) {
        val capture = imageCapture
        if (capture == null) {
            Log.w(TAG, "ImageCapture is null")
            return
        }

        Log.d(TAG, "Taking picture...")

        val name = SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
            .format(System.currentTimeMillis())
        val contentValues = android.content.ContentValues().apply {
            put(android.provider.MediaStore.MediaColumns.DISPLAY_NAME, name)
            put(android.provider.MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
            if (android.os.Build.VERSION.SDK_INT > android.os.Build.VERSION_CODES.P) {
                put(android.provider.MediaStore.Images.Media.RELATIVE_PATH, "Pictures/FaceDetection")
            }
        }

        val outputOptions = ImageCapture.OutputFileOptions.Builder(
            context.contentResolver,
            android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            contentValues
        ).build()

        capture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exception: ImageCaptureException) {
                    Log.e(TAG, "Photo capture failed: ${exception.message}", exception)
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    Log.d(TAG, "Photo capture succeeded: ${output.savedUri}")
                    output.savedUri?.let { uri ->
                        val bitmap = ImageUtils.loadAndRotateBitmap(context, uri, !currentIsBackCamera)
                        onImageCaptured(uri, bitmap)
                    }
                }
            }
        )
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            cameraProvider?.unbindAll()
            faceDetectorManager.release()
            cameraExecutor.shutdown()
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing camera resources", e)
        }
    }
}
