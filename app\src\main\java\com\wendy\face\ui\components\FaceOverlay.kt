package com.wendy.face.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PointMode
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.unit.dp
import com.google.mlkit.vision.facemesh.FaceMesh

/**
 * 人脸覆盖层组件
 * 在图片或相机预览上绘制人脸检测结果
 */
@Composable
fun FaceOverlay(faceMeshes: List<FaceMesh>, imageWidth: Int, imageHeight: Int, isBackCamera: Boolean) {
    // 添加调试日志
    android.util.Log.d("FaceOverlay", "FaceOverlay called with ${faceMeshes.size} faces, imageSize: ${imageWidth}x${imageHeight}, isBackCamera: $isBackCamera")

    Box(modifier = Modifier.fillMaxSize()) {
        if (faceMeshes.isNotEmpty()) {
            android.util.Log.d("FaceOverlay", "Displaying face points for ${faceMeshes.size} faces")
            // 将3D关键点显示在右下角固定区域
            Face3DPointsDisplay(
                faceMesh = faceMeshes.first(), // 只显示第一个检测到的人脸
                isBackCamera = isBackCamera,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 150.dp, end = 16.dp) // 向上移动，避开按钮
                    .size(120.dp)
            )
        } else {
            android.util.Log.d("FaceOverlay", "No faces to display")
        }
    }
}

/**
 * 3D人脸关键点显示组件 - 固定在右下角
 */
@Composable
private fun Face3DPointsDisplay(
    faceMesh: FaceMesh,
    isBackCamera: Boolean,
    modifier: Modifier = Modifier
) {
    android.util.Log.d("Face3DPointsDisplay", "Drawing face points, isBackCamera: $isBackCamera, points: ${faceMesh.allPoints.size}")
    Canvas(modifier = modifier) {
        val viewWidth = size.width
        val viewHeight = size.height
        android.util.Log.d("Face3DPointsDisplay", "Canvas size: ${viewWidth}x${viewHeight}")

        // 在这个独立区域内绘制3D关键点
        drawFace3DPoints(
            faceMesh = faceMesh,
            viewWidth = viewWidth,
            viewHeight = viewHeight,
            isBackCamera = isBackCamera
        )
    }
}


/**
 * 在独立区域绘制3D人脸关键点
 */
private fun DrawScope.drawFace3DPoints(
    faceMesh: FaceMesh,
    viewWidth: Float,
    viewHeight: Float,
    isBackCamera: Boolean
) {
    // 获取人脸边界框
    val boundingBox = faceMesh.boundingBox
    val faceWidth = boundingBox.width().toFloat()
    val faceHeight = boundingBox.height().toFloat()

    // 计算缩放比例，使人脸关键点适合显示区域
    val scaleX = viewWidth * 1.08f / faceWidth
    val scaleY = viewHeight * 1.08f / faceHeight
    val scale = scaleX.coerceAtMost(scaleY)

    // 计算居中偏移
    val scaledWidth = faceWidth * scale
    val scaledHeight = faceHeight * scale
    val offsetX = (viewWidth - scaledWidth) / 2f
    val offsetY = (viewHeight - scaledHeight) / 2f

    // 绘制关键点
    val points = faceMesh.allPoints.map { point ->
        val x = if (isBackCamera) {
            offsetX + (point.position.x - boundingBox.left) * scale
        } else {
            // 前置摄像头需要水平翻转
            offsetX + (boundingBox.right - point.position.x) * scale
        }
        val y = offsetY + (point.position.y - boundingBox.top) * scale
        Offset(x, y)
    }

    drawPoints(
        points = points,
        pointMode = PointMode.Points,
        color = Color.Cyan.copy(alpha = 0.9f),
        strokeWidth = 2.dp.toPx()
    )
}
