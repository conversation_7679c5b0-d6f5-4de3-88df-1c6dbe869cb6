package com.wendy.face.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PointMode
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.model.FacePalaces

/**
 * 人脸覆盖层组件
 * 在图片或相机预览上绘制人脸检测结果
 */
@Composable
fun FaceOverlay(faceMeshes: List<FaceMesh>, imageWidth: Int, imageHeight: Int, isBackCamera: Boolean) {
    // 添加调试日志
    android.util.Log.d("FaceOverlay", "FaceOverlay called with ${faceMeshes.size} faces, imageSize: ${imageWidth}x${imageHeight}, isBackCamera: $isBackCamera")

    Box(modifier = Modifier.fillMaxSize()) {
        if (faceMeshes.isNotEmpty()) {
            android.util.Log.d("FaceOverlay", "Displaying face points for ${faceMeshes.size} faces")

            // 在整个图片区域显示人脸关键点
            Canvas(modifier = Modifier.fillMaxSize()) {
                val canvasWidth = size.width
                val canvasHeight = size.height
                android.util.Log.d("FaceOverlay", "Canvas size: ${canvasWidth}x${canvasHeight}")

                // 计算图片在Canvas中的实际显示区域（ContentScale.Crop模式）
                val imageAspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
                val canvasAspectRatio = canvasWidth / canvasHeight

                val (displayWidth, displayHeight, offsetX, offsetY) = if (imageAspectRatio > canvasAspectRatio) {
                    // 图片更宽，按高度缩放，左右裁剪
                    val displayHeight = canvasHeight
                    val displayWidth = displayHeight * imageAspectRatio
                    val offsetX = (canvasWidth - displayWidth) / 2f
                    Tuple4(displayWidth, displayHeight, offsetX, 0f)
                } else {
                    // 图片更高，按宽度缩放，上下裁剪
                    val displayWidth = canvasWidth
                    val displayHeight = displayWidth / imageAspectRatio
                    val offsetY = (canvasHeight - displayHeight) / 2f
                    Tuple4(displayWidth, displayHeight, 0f, offsetY)
                }

                android.util.Log.d("FaceOverlay", "Display area: ${displayWidth}x${displayHeight}, offset: (${offsetX}, ${offsetY})")

                // 绘制人脸关键点
                faceMeshes.forEach { faceMesh ->
                    drawFacePoints(
                        faceMesh = faceMesh,
                        imageWidth = imageWidth,
                        imageHeight = imageHeight,
                        displayWidth = displayWidth,
                        displayHeight = displayHeight,
                        offsetX = offsetX,
                        offsetY = offsetY,
                        isBackCamera = isBackCamera
                    )
                }
            }

            // 同时在右下角显示3D关键点预览
            Face3DPointsDisplay(
                faceMesh = faceMeshes.first(),
                isBackCamera = isBackCamera,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 150.dp, end = 16.dp)
                    .size(120.dp)
            )
        } else {
            android.util.Log.d("FaceOverlay", "No faces to display")
        }
    }
}

// 辅助数据类
private data class Tuple4<T>(val first: T, val second: T, val third: T, val fourth: T)

/**
 * 3D人脸关键点显示组件 - 固定在右下角
 */
@Composable
private fun Face3DPointsDisplay(
    faceMesh: FaceMesh,
    isBackCamera: Boolean,
    modifier: Modifier = Modifier
) {
    android.util.Log.d("Face3DPointsDisplay", "Drawing face points, isBackCamera: $isBackCamera, points: ${faceMesh.allPoints.size}")
    Canvas(modifier = modifier) {
        val viewWidth = size.width
        val viewHeight = size.height
        android.util.Log.d("Face3DPointsDisplay", "Canvas size: ${viewWidth}x${viewHeight}")

        // 在这个独立区域内绘制3D关键点
        drawFace3DPoints(
            faceMesh = faceMesh,
            viewWidth = viewWidth,
            viewHeight = viewHeight,
            isBackCamera = isBackCamera
        )
    }
}


/**
 * 在图片上绘制人脸关键点
 */
private fun DrawScope.drawFacePoints(
    faceMesh: FaceMesh,
    imageWidth: Int,
    imageHeight: Int,
    displayWidth: Float,
    displayHeight: Float,
    offsetX: Float,
    offsetY: Float,
    isBackCamera: Boolean
) {
    // 计算从图片坐标到显示坐标的缩放比例
    val scaleX = displayWidth / imageWidth
    val scaleY = displayHeight / imageHeight

    android.util.Log.d("FaceOverlay", "Scale factors: scaleX=$scaleX, scaleY=$scaleY")

    // 绘制所有关键点
    val points = faceMesh.allPoints.map { point ->
        val x = if (isBackCamera) {
            offsetX + point.position.x * scaleX
        } else {
            // 前置摄像头需要水平翻转
            offsetX + (imageWidth - point.position.x) * scaleX
        }
        val y = offsetY + point.position.y * scaleY
        Offset(x, y)
    }

    // 绘制关键点
    drawPoints(
        points = points,
        pointMode = PointMode.Points,
        color = Color.Green.copy(alpha = 0.8f),
        strokeWidth = 3.dp.toPx()
    )

    // 绘制人脸边界框
    val boundingBox = faceMesh.boundingBox
    val left = if (isBackCamera) {
        offsetX + boundingBox.left * scaleX
    } else {
        offsetX + (imageWidth - boundingBox.right) * scaleX
    }
    val top = offsetY + boundingBox.top * scaleY
    val right = if (isBackCamera) {
        offsetX + boundingBox.right * scaleX
    } else {
        offsetX + (imageWidth - boundingBox.left) * scaleX
    }
    val bottom = offsetY + boundingBox.bottom * scaleY

    drawRect(
        color = Color.Red.copy(alpha = 0.6f),
        topLeft = Offset(left, top),
        size = androidx.compose.ui.geometry.Size(right - left, bottom - top),
        style = Stroke(width = 2.dp.toPx())
    )

    // 绘制12宫位标记
    draw12PalaceMarkers(
        faceMesh = faceMesh,
        imageWidth = imageWidth,
        imageHeight = imageHeight,
        scaleX = scaleX,
        scaleY = scaleY,
        offsetX = offsetX,
        offsetY = offsetY,
        isBackCamera = isBackCamera
    )
}

/**
 * 绘制12宫位标记
 */
private fun DrawScope.draw12PalaceMarkers(
    faceMesh: FaceMesh,
    imageWidth: Int,
    imageHeight: Int,
    scaleX: Float,
    scaleY: Float,
    offsetX: Float,
    offsetY: Float,
    isBackCamera: Boolean
) {
    val allPoints = faceMesh.allPoints

    // 为每个宫位绘制序号标记
    FacePalaces.palacePositions.forEach { palacePosition ->
        if (palacePosition.centerPoint < allPoints.size) {
            val centerPoint = allPoints[palacePosition.centerPoint]

            // 计算标记位置
            val x = if (isBackCamera) {
                offsetX + centerPoint.position.x * scaleX
            } else {
                // 前置摄像头需要水平翻转
                offsetX + (imageWidth - centerPoint.position.x) * scaleX
            }
            val y = offsetY + centerPoint.position.y * scaleY

            // 绘制圆形背景
            drawCircle(
                color = Color.Blue.copy(alpha = 0.8f),
                radius = 12.dp.toPx(),
                center = Offset(x, y)
            )

            // 绘制白色边框
            drawCircle(
                color = Color.White,
                radius = 12.dp.toPx(),
                center = Offset(x, y),
                style = Stroke(width = 1.dp.toPx())
            )

            // 绘制序号文字
            drawContext.canvas.nativeCanvas.apply {
                val paint = android.graphics.Paint().apply {
                    color = android.graphics.Color.WHITE
                    textSize = 14.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                    isAntiAlias = true
                    isFakeBoldText = true
                }

                // 计算文字垂直居中的位置
                val textHeight = paint.descent() - paint.ascent()
                val textOffset = textHeight / 2 - paint.descent()

                drawText(
                    palacePosition.number.toString(),
                    x,
                    y + textOffset,
                    paint
                )
            }
        }
    }
}

/**
 * 在独立区域绘制3D人脸关键点
 */
private fun DrawScope.drawFace3DPoints(
    faceMesh: FaceMesh,
    viewWidth: Float,
    viewHeight: Float,
    isBackCamera: Boolean
) {
    // 获取人脸边界框
    val boundingBox = faceMesh.boundingBox
    val faceWidth = boundingBox.width().toFloat()
    val faceHeight = boundingBox.height().toFloat()

    // 计算缩放比例，使人脸关键点适合显示区域
    val scaleX = viewWidth * 1.08f / faceWidth
    val scaleY = viewHeight * 1.08f / faceHeight
    val scale = scaleX.coerceAtMost(scaleY)

    // 计算居中偏移
    val scaledWidth = faceWidth * scale
    val scaledHeight = faceHeight * scale
    val offsetX = (viewWidth - scaledWidth) / 2f
    val offsetY = (viewHeight - scaledHeight) / 2f

    // 绘制关键点
    val points = faceMesh.allPoints.map { point ->
        val x = if (isBackCamera) {
            offsetX + (point.position.x - boundingBox.left) * scale
        } else {
            // 前置摄像头需要水平翻转
            offsetX + (boundingBox.right - point.position.x) * scale
        }
        val y = offsetY + (point.position.y - boundingBox.top) * scale
        Offset(x, y)
    }

    drawPoints(
        points = points,
        pointMode = PointMode.Points,
        color = Color.Cyan.copy(alpha = 0.9f),
        strokeWidth = 2.dp.toPx()
    )
}
