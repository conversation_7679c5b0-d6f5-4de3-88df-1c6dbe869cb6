package com.wendy.face.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import kotlin.math.cos
import kotlin.math.sin

/**
 * 人脸检测引导组件
 * 显示一个相机镜头样式的圆形区域来引导用户放置人脸，并提供提示信息。
 */
@Composable
fun FaceDetectionGuide() {
    BoxWithConstraints(modifier = Modifier.fillMaxSize()) {
        val circleRadiusDp = maxWidth * 0.4f

        Canvas(modifier = Modifier.fillMaxSize()) {
            val radius = circleRadiusDp.toPx()
            val center = size.center

            // 1. 绘制主圆环
            drawCircle(
                color = Color.White.copy(alpha = 0.9f),
                radius = radius,
                center = center,
                style = Stroke(width = 3.dp.toPx()) // 加粗主圆环
            )

            // 2. 绘制内圈
            drawCircle(
                color = Color.White.copy(alpha = 0.6f),
                radius = radius - 10.dp.toPx(), // 内圈半径稍小
                center = center,
                style = Stroke(width = 1.dp.toPx())
            )

            // 3. 绘制四个角的标记
            val cornerAngle = 20f // 角标记的弧度
            val cornerRadius = radius + 8.dp.toPx() // 将角标记向外偏移
            val strokeWidth = 4.dp.toPx()
            // 左上角
            drawArc(
                color = Color.White,
                startAngle = 180f,
                sweepAngle = cornerAngle,
                useCenter = false,
                topLeft = Offset(center.x - cornerRadius, center.y - cornerRadius),
                size = androidx.compose.ui.geometry.Size(cornerRadius * 2, cornerRadius * 2),
                style = Stroke(width = strokeWidth)
            )
            drawArc(
                color = Color.White,
                startAngle = 270f,
                sweepAngle = cornerAngle,
                useCenter = false,
                topLeft = Offset(center.x - cornerRadius, center.y - cornerRadius),
                size = androidx.compose.ui.geometry.Size(cornerRadius * 2, cornerRadius * 2),
                style = Stroke(width = strokeWidth)
            )
            // 右上角
            drawArc(
                color = Color.White,
                startAngle = 270f,
                sweepAngle = cornerAngle,
                useCenter = false,
                topLeft = Offset(center.x - cornerRadius, center.y - cornerRadius),
                size = androidx.compose.ui.geometry.Size(cornerRadius * 2, cornerRadius * 2),
                style = Stroke(width = strokeWidth)
            )
            drawArc(
                color = Color.White,
                startAngle = 0f,
                sweepAngle = cornerAngle,
                useCenter = false,
                topLeft = Offset(center.x - cornerRadius, center.y - cornerRadius),
                size = androidx.compose.ui.geometry.Size(cornerRadius * 2, cornerRadius * 2),
                style = Stroke(width = strokeWidth)
            )
            // 右下角
            drawArc(
                color = Color.White,
                startAngle = 0f,
                sweepAngle = cornerAngle,
                useCenter = false,
                topLeft = Offset(center.x - cornerRadius, center.y - cornerRadius),
                size = androidx.compose.ui.geometry.Size(cornerRadius * 2, cornerRadius * 2),
                style = Stroke(width = strokeWidth)
            )
            drawArc(
                color = Color.White,
                startAngle = 90f,
                sweepAngle = cornerAngle,
                useCenter = false,
                topLeft = Offset(center.x - cornerRadius, center.y - cornerRadius),
                size = androidx.compose.ui.geometry.Size(cornerRadius * 2, cornerRadius * 2),
                style = Stroke(width = strokeWidth)
            )
            // 左下角
            drawArc(
                color = Color.White,
                startAngle = 90f,
                sweepAngle = cornerAngle,
                useCenter = false,
                topLeft = Offset(center.x - cornerRadius, center.y - cornerRadius),
                size = androidx.compose.ui.geometry.Size(cornerRadius * 2, cornerRadius * 2),
                style = Stroke(width = strokeWidth)
            )
            drawArc(
                color = Color.White,
                startAngle = 180f,
                sweepAngle = cornerAngle,
                useCenter = false,
                topLeft = Offset(center.x - cornerRadius, center.y - cornerRadius),
                size = androidx.compose.ui.geometry.Size(cornerRadius * 2, cornerRadius * 2),
                style = Stroke(width = strokeWidth)
            )
        }

        // 在圆形上方添加提示
        Text(
            text = "[ 请将脸放于圆圈中，按下底部拍照按钮 ]",
            style = MaterialTheme.typography.titleSmall.copy(
                color = Color.White,
                fontWeight = FontWeight.Medium
            ),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = (maxHeight / 2) - circleRadiusDp - 60.dp) // 精确计算位置，确保在圆环外部上方
        )
    }
}
