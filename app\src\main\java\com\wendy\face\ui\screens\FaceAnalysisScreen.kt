package com.wendy.face.ui.screens

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.analyzer.PalaceAnalysisResult
import com.wendy.face.ui.components.FaceOverlay

/**
 * 人脸分析结果展示界面
 * @param capturedBitmap 拍摄的照片
 * @param faceMeshes 检测到的人脸网格
 * @param analysisResults 分析结果
 * @param isBackCamera 是否使用后置摄像头
 * @param onBack 返回按钮的回调
 */
@Composable
fun FaceAnalysisScreen(
    capturedBitmap: Bitmap,
    faceMeshes: List<FaceMesh>,
    analysisResults: List<PalaceAnalysisResult>,
    isBackCamera: Boolean,
    onBack: () -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 1. 将拍摄的照片作为背景
        Image(
            bitmap = capturedBitmap.asImageBitmap(),
            contentDescription = "Captured Photo",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )

        // 2. 在照片上叠加人脸关键点
        // 确保在分析界面也显示关键点，给用户直观感受
        FaceOverlay(
            faceMeshes = faceMeshes,
            imageWidth = capturedBitmap.width,
            imageHeight = capturedBitmap.height,
            isBackCamera = isBackCamera // 使用传入的摄像头类型参数
        )

        // 添加一个从下到上的渐变蒙层，让文字更清晰
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(Color.Transparent, Color.Black.copy(alpha = 0.8f)),
                        startY = 600f // 从屏幕大约1/3处开始渐变
                    )
                )
        )

        Column(modifier = Modifier.fillMaxSize()) {
            // 顶部返回按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.Start
            ) {
                Button(onClick = onBack) {
                    Text("重新分析")
                }
            }

            Spacer(modifier = Modifier.weight(1f))

            // 3. 底部展示分析结果的滚动列表
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 400.dp) // 限制最大高度
                    .padding(horizontal = 8.dp)
            ) {
                items(analysisResults) { result ->
                    AnalysisResultCard(result = result)
                }
            }
        }
    }
}

/**
 * 单个宫位分析结果的卡片UI
 */
@Composable
private fun AnalysisResultCard(result: PalaceAnalysisResult) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp, horizontal = 8.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.2f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = result.palaceName,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            Spacer(modifier = Modifier.height(6.dp))
            Text(
                text = result.description,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.9f),
                lineHeight = 22.sp
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "量化参考 -> 饱满度: ${"%.2f".format(result.fullness)}, 面积: ${"%.2f".format(result.area)}",
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.6f)
            )
        }
    }
}