package com.wendy.face.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.graphics.Rect
import android.media.ExifInterface
import android.net.Uri
import android.util.Log
import kotlin.math.max
import kotlin.math.min

/**
 * 图片处理工具类
 * 负责图片的加载、旋转、缩放等操作
 */
object ImageUtils {

    private const val TAG = "ImageUtils"

    /**
     * 从URI加载图片并自动处理旋转和镜像
     * @param context 上下文
     * @param uri 图片URI
     * @param needMirror 是否需要镜像翻转（前置摄像头需要）
     * @return 处理后的Bitmap，如果失败返回null
     */
    fun loadAndRotateBitmap(context: Context, uri: Uri, needMirror: Boolean = false): Bitmap? {
        return try {
            // 首先加载图片
            val inputStream = context.contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()

            if (bitmap == null) return null

            // 获取图片的EXIF信息来确定旋转角度
            val exifInputStream = context.contentResolver.openInputStream(uri)
            val exif = ExifInterface(exifInputStream!!)
            exifInputStream.close()

            val orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )

            val rotationAngle = when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> 90f
                ExifInterface.ORIENTATION_ROTATE_180 -> 180f
                ExifInterface.ORIENTATION_ROTATE_270 -> 270f
                else -> 0f
            }

            Log.d(TAG, "Original orientation: $orientation, rotation needed: $rotationAngle, needMirror: $needMirror")

            // 创建变换矩阵
            val matrix = Matrix()
            var needTransform = false

            // 处理旋转
            if (rotationAngle != 0f) {
                matrix.postRotate(rotationAngle)
                needTransform = true
            }

            // 处理前置摄像头镜像
            if (needMirror) {
                matrix.postScale(-1f, 1f, bitmap.width / 2f, bitmap.height / 2f)
                needTransform = true
            }

            if (needTransform) {
                // 应用变换
                val transformedBitmap = Bitmap.createBitmap(
                    bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
                )

                // 回收原始bitmap
                if (transformedBitmap != bitmap) {
                    bitmap.recycle()
                }

                Log.d(TAG, "Transformed bitmap: ${transformedBitmap.width}x${transformedBitmap.height}")
                transformedBitmap
            } else {
                bitmap
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading and rotating bitmap", e)
            null
        }
    }

    /**
     * 创建测试图片
     * @return 测试用的Bitmap
     */
    fun createTestBitmap(): Bitmap {
        // 创建一个简单的测试图片（纯色图片用于测试坐标系统）
        val testBitmap = Bitmap.createBitmap(400, 300, Bitmap.Config.ARGB_8888)
        testBitmap.eraseColor(android.graphics.Color.BLUE)

        // 在图片上绘制一些测试图形
        val canvas = android.graphics.Canvas(testBitmap)
        val paint = android.graphics.Paint().apply {
            color = android.graphics.Color.RED
            strokeWidth = 5f
            style = android.graphics.Paint.Style.STROKE
        }

        // 绘制一个矩形作为"假人脸"用于测试坐标
        canvas.drawRect(100f, 75f, 300f, 225f, paint)

        // 绘制一些圆点作为"假关键点"
        val pointPaint = android.graphics.Paint().apply {
            color = android.graphics.Color.YELLOW
            style = android.graphics.Paint.Style.FILL
        }
        canvas.drawCircle(150f, 125f, 8f, pointPaint) // 左眼
        canvas.drawCircle(250f, 125f, 8f, pointPaint) // 右眼
        canvas.drawCircle(200f, 150f, 8f, pointPaint) // 鼻子
        canvas.drawCircle(200f, 175f, 8f, pointPaint) // 嘴巴

        Log.d(TAG, "Created test bitmap: ${testBitmap.width} x ${testBitmap.height}")
        return testBitmap
    }

    /**
     * 计算图片在视图中的显示区域（考虑ContentScale.Fit）
     * @param imageWidth 图片宽度
     * @param imageHeight 图片高度
     * @param viewWidth 视图宽度
     * @param viewHeight 视图高度
     * @return Array<Float> [displayWidth, displayHeight, offsetX, offsetY]
     */
    fun calculateDisplayBounds(
        imageWidth: Int,
        imageHeight: Int,
        viewWidth: Float,
        viewHeight: Float
    ): Array<Float> {
        val imageAspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
        val viewAspectRatio = viewWidth / viewHeight

        return if (imageAspectRatio > viewAspectRatio) {
            // 图片更宽，以宽度为准
            val displayWidth = viewWidth
            val displayHeight = viewWidth / imageAspectRatio
            val offsetY = (viewHeight - displayHeight) / 2f
            arrayOf(displayWidth, displayHeight, 0f, offsetY)
        } else {
            // 图片更高，以高度为准
            val displayHeight = viewHeight
            val displayWidth = viewHeight * imageAspectRatio
            val offsetX = (viewWidth - displayWidth) / 2f
            arrayOf(displayWidth, displayHeight, offsetX, 0f)
        }
    }

    /**
     * 根据人脸检测结果裁剪头部区域
     * @param bitmap 原始图片
     * @param faceBoundingBoxes 检测到的人脸边界框列表
     * @return 裁剪后的头部图片，如果没有检测到人脸则返回原图
     */
    fun cropHeadRegion(bitmap: Bitmap, faceBoundingBoxes: List<Rect>): Bitmap {
        if (faceBoundingBoxes.isEmpty()) {
            Log.w(TAG, "No faces detected, returning original bitmap")
            return bitmap
        }

        try {
            // 仅处理检测到的第一个人脸
            val boundingBox = faceBoundingBoxes.first()

            val faceWidth = boundingBox.width()
            val faceHeight = boundingBox.height()

            // 定义扩展因子
            val topExpansion = (faceHeight * 0.5f).toInt()      // 向上扩展50%以包含头发
            val bottomExpansion = (faceHeight * 0.1f).toInt()   // 向下扩展10%以包含下巴
            val horizontalExpansion = (faceWidth * 0.2f).toInt()// 水平扩展20%

            // 基于人脸边界框进行扩展，计算新的裁剪区域
            val cropLeft = max(0, boundingBox.left - horizontalExpansion)
            val cropTop = max(0, boundingBox.top - topExpansion)
            val cropRight = min(bitmap.width, boundingBox.right + horizontalExpansion)
            val cropBottom = min(bitmap.height, boundingBox.bottom + bottomExpansion)

            val cropWidth = cropRight - cropLeft
            val cropHeight = cropBottom - cropTop

            if (cropWidth <= 0 || cropHeight <= 0) {
                Log.w(TAG, "Invalid crop dimensions, returning original bitmap")
                return bitmap
            }

            Log.d(TAG, "Original bitmap: ${bitmap.width}x${bitmap.height}")
            Log.d(TAG, "Face BBox: ${boundingBox}")
            Log.d(TAG, "Crop region: ($cropLeft, $cropTop) to ($cropRight, $cropBottom)")
            Log.d(TAG, "Crop size: ${cropWidth}x${cropHeight}")

            // 执行裁剪
            return Bitmap.createBitmap(bitmap, cropLeft, cropTop, cropWidth, cropHeight)

        } catch (e: Exception) {
            Log.e(TAG, "Error cropping head region", e)
            return bitmap
        }
    }
}
