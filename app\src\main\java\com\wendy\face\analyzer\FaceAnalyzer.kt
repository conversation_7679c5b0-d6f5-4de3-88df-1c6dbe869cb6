package com.wendy.face.analyzer

import com.google.mlkit.vision.facemesh.FaceMesh
import com.google.mlkit.vision.facemesh.FaceMeshPoint
import com.wendy.face.model.FacePalaces
import kotlin.math.abs

/**
 * 人脸分析结果的数据类
 */
data class PalaceAnalysisResult(
    val palaceName: String,
    val fullness: Double, // 饱满度, Z坐标的相对值
    val area: Double,     // 2D面积
    val description: String // 基于分析的简单描述
)

/**
 * 人脸分析器
 * 负责对面部十二宫进行几何特征分析
 */
class FaceAnalyzer {

    /**
     * 分析单张人脸的十二宫
     * @param faceMesh ML Kit返回的人脸网格数据
     * @return 返回一个包含所有宫位分析结果的列表
     */
    fun analyze(faceMesh: FaceMesh): List<PalaceAnalysisResult> {
        val allPoints = faceMesh.allPoints
        val results = mutableListOf<PalaceAnalysisResult>()

        // 计算整张脸的平均Z值，作为饱满度的参考基准
        val averageZ = allPoints.map { it.position.z }.average()

        // 按照12宫的顺序进行分析，确保结果有序
        FacePalaces.palacePositions.forEach { palacePosition ->
            val palaceName = palacePosition.name
            val palacePoints = FacePalaces.getPalacePoints(palaceName, allPoints)
            if (palacePoints.isNotEmpty()) {
                val fullness = calculateFullness(palacePoints, averageZ)
                val area = calculateArea(palacePoints)
                val description = generateDescription(palaceName, fullness, area)

                results.add(
                    PalaceAnalysisResult(
                        palaceName = "${palacePosition.circleNumber}${palaceName}", // 添加序号
                        fullness = fullness,
                        area = area,
                        description = description
                    )
                )
            }
        }
        return results
    }

    /**
     * 计算区域的饱满度
     * 通过比较区域内点的平均Z坐标与面部整体平均Z坐标得出
     * Z值越小，代表点越靠前（越饱满）
     * @param points 宫位区域的关键点
     * @param referenceZ 面部整体的平均Z坐标
     * @return 返回一个相对饱满度值。正值表示比平均更饱满，负值表示更凹陷。
     */
    private fun calculateFullness(points: List<FaceMeshPoint>, referenceZ: Double): Double {
        val averagePalaceZ = points.map { it.position.z }.average()
        // (referenceZ - averagePalaceZ) 将Z值反转，使其符合“越小越饱满”的直觉
        // 乘以-100来放大数值，便于观察
        return (referenceZ - averagePalaceZ) * 100
    }

    /**
     * 使用鞋带公式计算2D多边形的面积
     * @param points 宫位区域的关键点
     * @return 返回多边形的面积
     */
    private fun calculateArea(points: List<FaceMeshPoint>): Double {
        if (points.size < 3) return 0.0

        var area = 0.0
        for (i in points.indices) {
            val p1 = points[i]
            val p2 = points[(i + 1) % points.size]
            area += (p1.position.x * p2.position.y - p2.position.x * p1.position.y)
        }
        return abs(area / 2.0)
    }

    /**
     * 根据量化数据生成简单的描述性文本
     */
    private fun generateDescription(palaceName: String, fullness: Double, area: Double): String {
        val fullnessDesc = when {
            fullness > 5 -> "饱满"
            fullness < -5 -> "凹陷"
            else -> "平坦"
        }
        // 面积的描述比较复杂，需要相对参考，这里仅作示例
        val areaDesc = when {
            area > 2000 -> "开阔" // 这个阈值需要根据实际情况调整
            area < 500 -> "狭窄"
            else -> "适中"
        }

        return when (palaceName) {
            "命宫" -> "命宫区域$fullnessDesc，主掌一生命运、事业、心态。"
            "财帛宫" -> "鼻子为财帛宫，形状$areaDesc，主掌财富与事业。"
            "兄弟宫" -> "眉毛为兄弟宫，形态$areaDesc，主掌人际关系与兄弟情谊。"
            "夫妻宫" -> "眼尾为夫妻宫，区域$fullnessDesc，主掌婚姻与爱情。"
            "子女宫" -> "下眼睑为子女宫，区域$fullnessDesc，主掌子女运。"
            "疾厄宫" -> "鼻梁为疾厄宫，形态$fullnessDesc，主掌健康状况。"
            "迁移宫" -> "额角为迁移宫，区域$areaDesc，主掌出行、旅游、搬迁。"
            "奴仆宫" -> "下巴为奴仆宫，区域$fullnessDesc，主掌人际关系与领导力。"
            "官禄宫" -> "额头中央为官禄宫，区域$fullnessDesc 且 $areaDesc，主掌事业与官运。"
            "田宅宫" -> "上眼睑为田宅宫，区域$areaDesc，主掌家产与不动产。"
            "福德宫" -> "眉上区域为福德宫，$fullnessDesc，主掌福气与精神享受。"
            "父母宫" -> "日月角为父母宫，$fullnessDesc，主掌与父母的关系。"
            else -> "未知宫位"
        }
    }
}